require('dotenv').config();
const OpenAI = require('openai');

class GPTWrapper {
    constructor() {
        this.client = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
            baseURL: 'https://openrouter.ai/api/v1',
            defaultHeaders: {
                "HTTP-Referer": "http://localhost:3000", // Optional, for including your app on openrouter.ai rankings.
                "X-Title": "GPT Wrapper", // Optional. Shows in rankings on openrouter.ai.
            }
        });
    }

    /**
     * Send a message to GPT and get a response
     * @param {string} message - The message to send
     * @param {Object} options - Optional parameters
     * @param {string} options.model - The model to use (default: gpt-3.5-turbo)
     * @param {number} options.maxTokens - Maximum tokens in response
     * @param {number} options.temperature - Creativity level (0-2)
     * @param {string} options.systemPrompt - System prompt to set context
     * @returns {Promise<string>} The GPT response
     */
    async chat(message, options = {}) {
        const {
            model = 'gpt-3.5-turbo',
            maxTokens = 1000,
            temperature = 0.7,
            systemPrompt = 'You are a helpful assistant.'
        } = options;

        try {
            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: message }
            ];

            const response = await this.client.chat.completions.create({
                model: model,
                messages: messages,
                max_tokens: maxTokens,
                temperature: temperature,
            });

            return response.choices[0].message.content;
        } catch (error) {
            console.error('Error calling OpenAI API:', error.message);
            throw error;
        }
    }

    /**
     * Start a conversation with context
     * @param {Array} messages - Array of message objects with role and content
     * @param {Object} options - Optional parameters
     * @returns {Promise<string>} The GPT response
     */
    async conversation(messages, options = {}) {
        const {
            model = 'gpt-3.5-turbo',
            maxTokens = 1000,
            temperature = 0.7,
        } = options;

        try {
            const response = await this.client.chat.completions.create({
                model: model,
                messages: messages,
                max_tokens: maxTokens,
                temperature: temperature,
            });

            return response.choices[0].message.content;
        } catch (error) {
            console.error('Error calling OpenAI API:', error.message);
            throw error;
        }
    }

    /**
     * Generate text completion
     * @param {string} prompt - The prompt to complete
     * @param {Object} options - Optional parameters
     * @returns {Promise<string>} The completion
     */
    async complete(prompt, options = {}) {
        return this.chat(prompt, options);
    }

    /**
     * Test the API connection
     * @returns {Promise<boolean>} True if connection is successful
     */
    async testConnection() {
        try {
            const response = await this.chat('Hello! Just testing the connection.', {
                maxTokens: 50
            });
            console.log('✅ API connection successful!');
            console.log('Response:', response);
            return true;
        } catch (error) {
            console.error('❌ API connection failed:', error.message);
            return false;
        }
    }
}

module.exports = GPTWrapper;
