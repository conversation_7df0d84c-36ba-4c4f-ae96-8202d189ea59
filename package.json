{"name": "gpt-wrapper", "version": "1.0.0", "description": "A simple GPT wrapper for testing OpenAI API", "main": "gpt-wrapper.js", "scripts": {"start": "node server.js", "cli": "node cli.js", "test": "node cli.js test", "examples": "node examples.js", "dev": "node server.js"}, "keywords": ["openai", "gpt", "ai", "wrapper", "api"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "openai": "^5.3.0"}}