const express = require('express');
const cors = require('cors');
const path = require('path');
const GPTWrapper = require('./gpt-wrapper');

const app = express();
const port = process.env.PORT || 3000;
const gpt = new GPTWrapper();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/chat', async (req, res) => {
    try {
        const { message, options = {} } = req.body;
        
        if (!message) {
            return res.status(400).json({ error: 'Message is required' });
        }
        
        const response = await gpt.chat(message, options);
        res.json({ response });
    } catch (error) {
        console.error('Chat error:', error);
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/conversation', async (req, res) => {
    try {
        const { messages, options = {} } = req.body;
        
        if (!messages || !Array.isArray(messages)) {
            return res.status(400).json({ error: 'Messages array is required' });
        }
        
        const response = await gpt.conversation(messages, options);
        res.json({ response });
    } catch (error) {
        console.error('Conversation error:', error);
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/test', async (req, res) => {
    try {
        const success = await gpt.testConnection();
        res.json({ success, message: success ? 'API connection successful' : 'API connection failed' });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.listen(port, () => {
    console.log(`🚀 GPT Wrapper server running at http://localhost:${port}`);
    console.log(`📝 Web interface: http://localhost:${port}`);
    console.log(`🔧 API endpoints:`);
    console.log(`   POST /api/chat - Send a single message`);
    console.log(`   POST /api/conversation - Send conversation with context`);
    console.log(`   GET  /api/test - Test API connection`);
});
