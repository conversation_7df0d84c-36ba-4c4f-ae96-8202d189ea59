# GPT Wrapper 🤖

A simple and easy-to-use wrapper for the OpenAI GPT API. Perfect for testing your API key and building quick AI-powered applications.

## Features

- ✅ Simple programmatic interface
- 🖥️ Command-line interface (CLI)
- 🌐 Web interface for browser testing
- 🔧 RESTful API endpoints
- 📝 Multiple usage examples
- 🎛️ Configurable options (temperature, max tokens, system prompts)
- 💬 Support for conversations with context

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up your API key:**
   Your API key is already configured in `.env`:
   ```
   OPENAI_API_KEY=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
   ```

3. **Test the connection:**
   ```bash
   npm test
   ```

## Usage Options

### 1. Web Interface (Recommended for testing)
Start the web server:
```bash
npm start
```
Then open http://localhost:3000 in your browser.

### 2. Command Line Interface
Interactive mode:
```bash
npm run cli
```

Single message:
```bash
node cli.js "Tell me a joke"
```

### 3. Programmatic Usage
```javascript
const GPTWrapper = require('./gpt-wrapper');
const gpt = new GPTWrapper();

// Simple chat
const response = await gpt.chat('Hello, how are you?');
console.log(response);

// With options
const response = await gpt.chat('Write a poem', {
    temperature: 1.2,
    maxTokens: 200,
    systemPrompt: 'You are a creative poet.'
});

// Conversation with context
const messages = [
    { role: 'system', content: 'You are a helpful assistant.' },
    { role: 'user', content: 'What is 2+2?' },
    { role: 'assistant', content: '2+2 equals 4.' },
    { role: 'user', content: 'What about 2+3?' }
];
const response = await gpt.conversation(messages);
```

### 4. REST API
Start the server and use these endpoints:

- `POST /api/chat` - Send a single message
- `POST /api/conversation` - Send conversation with context  
- `GET /api/test` - Test API connection

Example:
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello!"}'
```

## Available Scripts

- `npm start` - Start the web server
- `npm run cli` - Start interactive CLI
- `npm test` - Test API connection
- `npm run examples` - Run usage examples
- `npm run dev` - Start development server

## Configuration Options

All methods accept an options object:

```javascript
{
    model: 'gpt-3.5-turbo',     // Model to use
    maxTokens: 1000,            // Maximum response length
    temperature: 0.7,           // Creativity (0-2)
    systemPrompt: 'You are...'  // System context
}
```

## Examples

Run the examples to see different use cases:
```bash
npm run examples
```

This will demonstrate:
- Simple chat
- Creative writing with high temperature
- Conversations with context
- Code explanation
- Different models (if available)

## Troubleshooting

1. **API Key Issues**: Make sure your API key is correctly set in `.env`
2. **Network Errors**: Check your internet connection
3. **Model Not Available**: Some models might not be available with your API key
4. **Rate Limits**: OpenAI has rate limits - wait a moment between requests

## File Structure

```
├── gpt-wrapper.js      # Main wrapper class
├── cli.js              # Command-line interface
├── server.js           # Web server
├── examples.js         # Usage examples
├── public/
│   └── index.html      # Web interface
├── .env                # Environment variables
└── README.md           # This file
```

## API Reference

### GPTWrapper Class

#### `chat(message, options)`
Send a single message to GPT.

#### `conversation(messages, options)`  
Send a conversation with context.

#### `complete(prompt, options)`
Alias for `chat()`.

#### `testConnection()`
Test if the API key works.

---

Happy coding! 🚀
