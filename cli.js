#!/usr/bin/env node

const GPTWrapper = require('./gpt-wrapper');
const readline = require('readline');

const gpt = new GPTWrapper();

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🤖 GPT Wrapper CLI');
console.log('Type your message and press Enter. Type "exit" to quit, "test" to test connection.');
console.log('Commands: test, exit, clear, help');
console.log('---');

// Handle command line arguments
const args = process.argv.slice(2);
if (args.length > 0) {
    const message = args.join(' ');
    if (message === 'test') {
        testConnection();
    } else {
        sendMessage(message);
    }
} else {
    startInteractiveMode();
}

async function testConnection() {
    console.log('Testing API connection...');
    const success = await gpt.testConnection();
    if (!success) {
        process.exit(1);
    }
    process.exit(0);
}

async function sendMessage(message) {
    try {
        console.log('🤔 Thinking...');
        const response = await gpt.chat(message);
        console.log('\n🤖 GPT Response:');
        console.log(response);
        console.log('\n---');
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

function startInteractiveMode() {
    function askQuestion() {
        rl.question('You: ', async (input) => {
            const message = input.trim();
            
            if (message.toLowerCase() === 'exit') {
                console.log('Goodbye! 👋');
                rl.close();
                return;
            }
            
            if (message.toLowerCase() === 'test') {
                await testConnection();
                askQuestion();
                return;
            }
            
            if (message.toLowerCase() === 'clear') {
                console.clear();
                console.log('🤖 GPT Wrapper CLI - Chat cleared');
                askQuestion();
                return;
            }
            
            if (message.toLowerCase() === 'help') {
                console.log('\nAvailable commands:');
                console.log('- test: Test API connection');
                console.log('- clear: Clear the screen');
                console.log('- exit: Exit the CLI');
                console.log('- help: Show this help message');
                console.log('Or just type any message to chat with GPT!\n');
                askQuestion();
                return;
            }
            
            if (message) {
                await sendMessage(message);
            }
            
            askQuestion();
        });
    }
    
    askQuestion();
}
