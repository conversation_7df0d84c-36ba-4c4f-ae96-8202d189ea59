const GPTWrapper = require('./gpt-wrapper');

// Initialize the wrapper
const gpt = new GPTWrapper();

async function runExamples() {
    console.log('🚀 Running GPT Wrapper Examples\n');

    try {
        // Example 1: Simple chat
        console.log('📝 Example 1: Simple Chat');
        const response1 = await gpt.chat('Hello! Can you tell me a joke?');
        console.log('Response:', response1);
        console.log('\n---\n');

        // Example 2: Chat with custom options
        console.log('📝 Example 2: Creative Writing');
        const response2 = await gpt.chat('Write a short poem about coding', {
            temperature: 1.2, // More creative
            maxTokens: 200,
            systemPrompt: 'You are a creative poet who loves technology.'
        });
        console.log('Response:', response2);
        console.log('\n---\n');

        // Example 3: Conversation with context
        console.log('📝 Example 3: Conversation with Context');
        const messages = [
            { role: 'system', content: 'You are a helpful math tutor.' },
            { role: 'user', content: 'What is 15 + 27?' },
            { role: 'assistant', content: '15 + 27 = 42' },
            { role: 'user', content: 'Now multiply that result by 3' }
        ];
        const response3 = await gpt.conversation(messages);
        console.log('Response:', response3);
        console.log('\n---\n');

        // Example 4: Code explanation
        console.log('📝 Example 4: Code Explanation');
        const codeToExplain = `
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}
        `;
        const response4 = await gpt.chat(`Explain this code and suggest improvements:\n${codeToExplain}`, {
            systemPrompt: 'You are an expert programmer who explains code clearly and suggests optimizations.'
        });
        console.log('Response:', response4);
        console.log('\n---\n');

        // Example 5: Different models (if available)
        console.log('📝 Example 5: Using Different Model');
        try {
            const response5 = await gpt.chat('Explain quantum computing in simple terms', {
                model: 'gpt-4', // This might not be available with your API key
                maxTokens: 150
            });
            console.log('Response:', response5);
        } catch (error) {
            console.log('GPT-4 not available, trying with default model...');
            const response5 = await gpt.chat('Explain quantum computing in simple terms', {
                maxTokens: 150
            });
            console.log('Response:', response5);
        }

    } catch (error) {
        console.error('❌ Error running examples:', error.message);
    }
}

// Run examples if this file is executed directly
if (require.main === module) {
    runExamples();
}

module.exports = { runExamples };
