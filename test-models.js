const GPTWrapper = require('./gpt-wrapper');

// List of free models available on OpenRouter
const FREE_MODELS = [
    'deepseek/deepseek-chat',
    'google/gemma-7b-it:free',
    'mistralai/mistral-7b-instruct:free',
    'huggingface/starcoder2-15b:free',
    'nousresearch/nous-capybara-7b:free',
    'openchat/openchat-7b:free',
    'gryphe/mythomist-7b:free',
    'undi95/toppy-m-7b:free'
];

const gpt = new GPTWrapper();

async function testModels() {
    console.log('🧪 Testing Free Models on OpenRouter\n');
    console.log('This will test which models are currently working with your API key.\n');

    const testMessage = 'Hello! Please respond with just "Hi there!" to test the connection.';
    const workingModels = [];
    const failedModels = [];

    for (const model of FREE_MODELS) {
        console.log(`Testing ${model}...`);
        
        try {
            const response = await gpt.chat(testMessage, {
                model: model,
                maxTokens: 50,
                temperature: 0.1
            });
            
            console.log(`✅ ${model} - Working!`);
            console.log(`   Response: ${response.substring(0, 100)}${response.length > 100 ? '...' : ''}`);
            workingModels.push(model);
            
        } catch (error) {
            console.log(`❌ ${model} - Failed: ${error.message}`);
            failedModels.push({ model, error: error.message });
        }
        
        console.log(''); // Empty line for readability
        
        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n📊 Summary:');
    console.log(`✅ Working models (${workingModels.length}):`);
    workingModels.forEach(model => console.log(`   - ${model}`));
    
    console.log(`\n❌ Failed models (${failedModels.length}):`);
    failedModels.forEach(({model, error}) => {
        console.log(`   - ${model}: ${error}`);
    });

    if (workingModels.length > 0) {
        console.log(`\n🎉 Great! You can use any of the working models above.`);
        console.log(`💡 To use a different model, update the default in gpt-wrapper.js or pass it as an option:`);
        console.log(`   const response = await gpt.chat('Hello', { model: '${workingModels[0]}' });`);
    } else {
        console.log(`\n😞 No models are currently working. This might be because:`);
        console.log(`   - Your API key has exceeded its free limit`);
        console.log(`   - There's a temporary issue with OpenRouter`);
        console.log(`   - Your API key needs to be refreshed`);
        console.log(`\n💡 Visit https://openrouter.ai/settings/keys to check your key status.`);
    }
}

// Run if this file is executed directly
if (require.main === module) {
    testModels().catch(console.error);
}

module.exports = { testModels, FREE_MODELS };
